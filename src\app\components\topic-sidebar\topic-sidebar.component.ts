import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CourseService } from '../../services/course.service';
import { Course, Topic, SubTopic } from '../../models/course.model';

@Component({
  selector: 'app-topic-sidebar',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="topic-sidebar">
      <div class="bookmarks-header">
        Bookmarks & Annotations
      </div>

      <div class="sidebar-header">
        <h5 class="mb-0">Course Structure</h5>
      </div>
      
      <div class="topics-list">
        @if (course(); as courseData) {
          <!-- DEBUG: Course data loaded: {{ courseData.topics.length }} topics -->
          @for (topic of courseData.topics; track topic.id) {
            <div class="topic-item">
              <div 
                class="topic-header"
                [class.active]="selectedTopic()?.id === topic.id"
                (click)="toggleTopic(topic)"
              >
                <div class="topic-title">
                  <span>{{ topic.title }}</span>
                  @if (topic.subTopics && topic.subTopics.length > 0) {
                    <i
                      class="expand-icon"
                      [class.expanded]="topic.expanded"
                    >
                      {{ topic.expanded ? '▼' : '▶' }}
                    </i>
                  }
                </div>
              </div>
              
              @if (topic.expanded && topic.subTopics) {
                <div class="subtopics-list">
                  @for (subTopic of topic.subTopics; track subTopic.id) {
                    <div
                      class="subtopic-item"
                      [class.active]="selectedSubTopic()?.id === subTopic.id"
                      (click)="selectSubTopic(subTopic)"
                    >
                      <div class="subtopic-bullet"></div>
                      <div class="subtopic-content">
                        <div class="subtopic-title">
                          {{ subTopic.title }}@if (subTopic.pages && subTopic.pages.length > 1) {<span class="page-indicator"> ({{ subTopic.currentPage }} of {{ subTopic.pages.length }})</span>}
                        </div>
                      </div>
                    </div>
                  }
                </div>
              }
              
              @if (!topic.subTopics && topic.pages) {
                <div class="topic-pages">
                  @if (topic.pages.length > 1) {
                    <div class="page-indicator">
                      Page {{ topic.currentPage }} of {{ topic.pages.length }}
                    </div>
                  }
                </div>
              }
            </div>
          }
        }
      </div>

      <!-- DYNAMICS SECTION (Manual fallback) -->
      <div class="topic-item">
        <div class="topic-header">
          <div class="topic-title">
            <span>Dynamics</span>
            <i class="expand-icon expanded">▼</i>
          </div>
        </div>
        <div class="subtopics-list">
          <div class="subtopic-item active">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Rectilinear motion <span class="page-indicator">(1 of 4)</span></div>
            </div>
          </div>
          <div class="subtopic-item">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Projectile motion</div>
            </div>
          </div>
          <div class="subtopic-item">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Tangential and normal components</div>
            </div>
          </div>
          <div class="subtopic-item">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Motion in polar co-ordinates</div>
            </div>
          </div>
          <div class="subtopic-item">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Kinetics of rectilinear motion</div>
            </div>
          </div>
          <div class="subtopic-item">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Kinetics of curvilinear Motion</div>
            </div>
          </div>
          <div class="subtopic-item">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Work energy</div>
            </div>
          </div>
          <div class="subtopic-item">
            <div class="subtopic-bullet"></div>
            <div class="subtopic-content">
              <div class="subtopic-title">Impulse momentum and impact</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Statics Topic -->
      <div class="topic-item">
        <div
          class="topic-header"
          (click)="toggleStaticsTopic()"
        >
          <div class="topic-title">
            <span>Statics</span>
            <i
              class="expand-icon"
              [class.expanded]="staticsExpanded"
            >
              {{ staticsExpanded ? '▼' : '▶' }}
            </i>
          </div>
        </div>
        @if (staticsExpanded) {
          <div class="subtopics-list">
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Resultant of force system</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Center of gravity and moment of inertia</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Equilibrium</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Beams</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Trusses</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Cables</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Frames</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Friction</div>
              </div>
            </div>
            <div class="subtopic-item">
              <div class="subtopic-bullet"></div>
              <div class="subtopic-content">
                <div class="subtopic-title">Space Forces</div>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  `,
  styleUrls: ['./topic-sidebar.component.css']
})
export class TopicSidebarComponent implements OnInit {
  private courseService = inject(CourseService);

  course = this.courseService.course;
  selectedTopic = this.courseService.selectedTopic;
  selectedSubTopic = this.courseService.selectedSubTopic;

  staticsExpanded = true;

  ngOnInit() {}

  toggleTopic(topic: Topic) {
    // DON'T COLLAPSE - KEEP BOTH SECTIONS ALWAYS OPEN
    // Just select the topic without changing expanded state
    this.courseService.selectTopic(topic);
  }

  selectSubTopic(subTopic: SubTopic) {
    this.courseService.selectSubTopic(subTopic);
  }

  toggleStaticsTopic() {
    // DON'T COLLAPSE - KEEP STATICS ALWAYS OPEN
    // this.staticsExpanded = !this.staticsExpanded;
  }
}
