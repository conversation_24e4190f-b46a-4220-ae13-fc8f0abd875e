import { Injectable } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ContentItem } from './api.service';

export interface DiagramConfig {
  type: 'translation' | 'rotation' | 'custom';
  shapes: DiagramShape[];
  arrows: DiagramArrow[];
  labels: DiagramLabel[];
}

export interface DiagramShape {
  id: string;
  type: 'rectangle' | 'circle' | 'polygon';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  style?: any;
}

export interface DiagramArrow {
  id: string;
  from: { x: number; y: number };
  to: { x: number; y: number };
  type: 'straight' | 'curved';
  style?: any;
}

export interface DiagramLabel {
  id: string;
  text: string;
  x: number;
  y: number;
  style?: any;
}

@Injectable({
  providedIn: 'root'
})
export class ContentRendererService {

  constructor(private sanitizer: DomSanitizer) {}

  renderContent(items: ContentItem[]): SafeHtml {
    let html = '';
    
    items.sort((a, b) => a.order - b.order);
    
    for (const item of items) {
      switch (item.type) {
        case 'text':
          html += this.renderText(item);
          break;
        case 'diagram':
          html += this.renderDiagram(item);
          break;
        case 'audio':
          html += this.renderAudio(item);
          break;
        case 'video':
          html += this.renderVideo(item);
          break;
        case 'image':
          html += this.renderImage(item);
          break;
      }
    }
    
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  private renderText(item: ContentItem): string {
    const content = item.content;
    const metadata = item.metadata || {};
    
    // Apply text formatting based on metadata
    let formattedContent = content;
    
    if (metadata.highlights) {
      formattedContent = this.applyHighlights(formattedContent, metadata.highlights);
    }
    
    if (metadata.bold) {
      formattedContent = `<strong>${formattedContent}</strong>`;
    }
    
    if (metadata.italic) {
      formattedContent = `<em>${formattedContent}</em>`;
    }
    
    const className = metadata.className || '';
    const style = metadata.style || '';
    
    return `
      <div class="content-item ${className}" style="${style}" data-item-id="${item.id}">
        ${formattedContent}
      </div>
    `;
  }

  private renderDiagram(item: ContentItem): string {
    const config: DiagramConfig = item.metadata?.config;
    
    if (!config) {
      return `<div class="content-item">Invalid diagram configuration</div>`;
    }
    
    switch (config.type) {
      case 'translation':
        return this.renderTranslationDiagram(item, config);
      case 'rotation':
        return this.renderRotationDiagram(item, config);
      default:
        return this.renderCustomDiagram(item, config);
    }
  }

  private renderTranslationDiagram(item: ContentItem, config: DiagramConfig): string {
    // Generate the translation diagram HTML
    return `
      <div class="content-item" data-item-id="${item.id}">
        <div class="diagram-container">
          <div class="diagram-section">
            <h4 class="diagram-title">Rectilinear translation</h4>
            <div class="diagram-content">
              <div class="shape-box">
                <span class="corner-label top-left">A</span>
                <span class="corner-label top-right">B</span>
                <span class="corner-label bottom-left">C</span>
                <span class="corner-label bottom-right">D</span>
              </div>
              <div class="arrow-right">→</div>
              <div class="shape-box">
                <span class="corner-label top-left">A'</span>
                <span class="corner-label top-right">B'</span>
                <span class="corner-label bottom-left">C'</span>
                <span class="corner-label bottom-right">D'</span>
              </div>
            </div>
          </div>
          
          <div class="diagram-section">
            <h4 class="diagram-title">Curvilinear translation</h4>
            <div class="diagram-content">
              <div class="shape-box">
                <span class="corner-label top-left">A</span>
                <span class="corner-label top-right">B</span>
                <span class="corner-label bottom-left">C</span>
                <span class="corner-label bottom-right">D</span>
              </div>
              <div class="arrow-curved">⤴</div>
              <div class="shape-box rotated">
                <span class="corner-label top-left">A'</span>
                <span class="corner-label top-right">B'</span>
                <span class="corner-label bottom-left">C'</span>
                <span class="corner-label bottom-right">D'</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderRotationDiagram(item: ContentItem, config: DiagramConfig): string {
    return `
      <div class="content-item" data-item-id="${item.id}">
        <div class="rotation-diagram">
          <h4>Rotation Motion</h4>
          <div class="rotation-content">
            <svg width="200" height="200" viewBox="0 0 200 200">
              <circle cx="100" cy="100" r="80" fill="none" stroke="#333" stroke-width="2"/>
              <circle cx="100" cy="100" r="4" fill="#333"/>
              <line x1="100" y1="100" x2="180" y2="100" stroke="#007bff" stroke-width="2"/>
              <text x="185" y="105" font-size="12" fill="#333">r</text>
              <path d="M 140 60 A 50 50 0 0 1 160 140" fill="none" stroke="#007bff" stroke-width="2" marker-end="url(#arrowhead)"/>
              <text x="170" y="100" font-size="12" fill="#007bff">ω</text>
            </svg>
          </div>
        </div>
      </div>
    `;
  }

  private renderCustomDiagram(item: ContentItem, config: DiagramConfig): string {
    // Render custom diagrams based on configuration
    let svgContent = '';
    
    // Render shapes
    for (const shape of config.shapes) {
      svgContent += this.renderShape(shape);
    }
    
    // Render arrows
    for (const arrow of config.arrows) {
      svgContent += this.renderArrow(arrow);
    }
    
    // Render labels
    for (const label of config.labels) {
      svgContent += this.renderLabel(label);
    }
    
    return `
      <div class="content-item" data-item-id="${item.id}">
        <div class="custom-diagram">
          <svg width="400" height="300" viewBox="0 0 400 300">
            ${svgContent}
          </svg>
        </div>
      </div>
    `;
  }

  private renderShape(shape: DiagramShape): string {
    const style = shape.style ? this.objectToStyle(shape.style) : 'fill:white;stroke:#333;stroke-width:2';
    const transform = shape.rotation ? `transform="rotate(${shape.rotation} ${shape.x + shape.width/2} ${shape.y + shape.height/2})"` : '';
    
    switch (shape.type) {
      case 'rectangle':
        return `<rect x="${shape.x}" y="${shape.y}" width="${shape.width}" height="${shape.height}" style="${style}" ${transform}/>`;
      case 'circle':
        return `<circle cx="${shape.x}" cy="${shape.y}" r="${shape.width/2}" style="${style}" ${transform}/>`;
      default:
        return '';
    }
  }

  private renderArrow(arrow: DiagramArrow): string {
    const style = arrow.style ? this.objectToStyle(arrow.style) : 'stroke:#007bff;stroke-width:2';
    
    if (arrow.type === 'curved') {
      const midX = (arrow.from.x + arrow.to.x) / 2;
      const midY = (arrow.from.y + arrow.to.y) / 2 - 20;
      return `<path d="M ${arrow.from.x} ${arrow.from.y} Q ${midX} ${midY} ${arrow.to.x} ${arrow.to.y}" style="${style}" marker-end="url(#arrowhead)"/>`;
    } else {
      return `<line x1="${arrow.from.x}" y1="${arrow.from.y}" x2="${arrow.to.x}" y2="${arrow.to.y}" style="${style}" marker-end="url(#arrowhead)"/>`;
    }
  }

  private renderLabel(label: DiagramLabel): string {
    const style = label.style ? this.objectToStyle(label.style) : 'font-size:12px;fill:#333';
    return `<text x="${label.x}" y="${label.y}" style="${style}">${label.text}</text>`;
  }

  private renderAudio(item: ContentItem): string {
    return `
      <div class="content-item" data-item-id="${item.id}">
        <div class="audio-content">
          <audio controls>
            <source src="${item.content}" type="audio/mpeg">
            Your browser does not support the audio element.
          </audio>
        </div>
      </div>
    `;
  }

  private renderVideo(item: ContentItem): string {
    return `
      <div class="content-item" data-item-id="${item.id}">
        <div class="video-content">
          <video controls width="100%">
            <source src="${item.content}" type="video/mp4">
            Your browser does not support the video element.
          </video>
        </div>
      </div>
    `;
  }

  private renderImage(item: ContentItem): string {
    const alt = item.metadata?.alt || 'Image';
    const caption = item.metadata?.caption;
    
    return `
      <div class="content-item" data-item-id="${item.id}">
        <div class="image-content">
          <img src="${item.content}" alt="${alt}" style="max-width: 100%; height: auto;">
          ${caption ? `<p class="image-caption">${caption}</p>` : ''}
        </div>
      </div>
    `;
  }

  private applyHighlights(content: string, highlights: any[]): string {
    let result = content;
    
    // Sort highlights by position to avoid conflicts
    highlights.sort((a, b) => a.startOffset - b.startOffset);
    
    for (const highlight of highlights) {
      const before = result.substring(0, highlight.startOffset);
      const highlighted = result.substring(highlight.startOffset, highlight.endOffset);
      const after = result.substring(highlight.endOffset);
      
      result = before + `<span class="highlight-${highlight.color}">${highlighted}</span>` + after;
    }
    
    return result;
  }

  private objectToStyle(obj: any): string {
    return Object.entries(obj)
      .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}:${value}`)
      .join(';');
  }
}
