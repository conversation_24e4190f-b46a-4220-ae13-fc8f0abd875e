.topic-sidebar {
  height: 100%;
  background-color: #FFFBF3;
  border-right: 1px solid #dee2e6;
  font-family: 'Montserrat', sans-serif;
  display: flex;
  flex-direction: column;
}

.bookmarks-header {
  padding: 16px 20px;
  background-color: #343a40;
  margin: 16px;
  border-radius: 25px;
}

.bookmarks-btn {
  width: 100%;
  padding: 8px 16px;
  background-color: transparent;
  color: white;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bookmarks-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-header {
  padding: 0 20px 16px 20px;
  border-bottom: none;
  background-color: transparent;
}

.sidebar-header h5 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #333;
  margin: 0;
}

.topics-list {
  padding: 0 20px;
  flex: 1;
  overflow-y: auto;
}

.topic-item {
  border-bottom: none;
  margin-bottom: 24px;
}

.topic-header {
  padding: 12px 0;
  cursor: pointer;
  transition: color 0.2s ease;
  border-left: none;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.topic-header:hover {
  background-color: transparent;
}

.topic-header.active {
  background-color: transparent;
  border-left: none;
}

.topic-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 15px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #495057;
}

.topic-header:hover .topic-title {
  color: #495057; /* NO COLOR CHANGE ON HOVER */
}

.expand-icon {
  font-size: 12px;
  color: #666;
  transition: transform 0.2s ease;
  width: 12px;
  display: inline-block;
  margin-left: auto; /* PUSH TO RIGHT SIDE */
  margin-right: 0;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.subtopics-list {
  background-color: transparent;
  border-top: none;
  margin-top: 12px;
  margin-left: 16px;
}

.subtopic-item {
  padding: 8px 0;
  cursor: pointer;
  transition: color 0.2s ease;
  border-left: none;
  border-bottom: none;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.subtopic-item:hover {
  background-color: transparent;
}

.subtopic-item.active {
  background-color: transparent;
  border-left: none;
}

.subtopic-bullet {
  width: 4px;
  height: 4px;
  background-color: #666;
  border-radius: 50%;
  margin-top: 6px;
  flex-shrink: 0;
}

.subtopic-item.active .subtopic-bullet {
  background-color: #666; /* SAME COLOR AS NORMAL BULLETS */
}

.subtopic-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.subtopic-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  color: #666;
}

.subtopic-item:hover .subtopic-title {
  color: #666; /* NO COLOR CHANGE ON HOVER */
}

.subtopic-item.active .subtopic-title {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 15px;
  line-height: 100%;
  letter-spacing: 0%;
  text-decoration: underline;
  text-decoration-style: solid;
  text-decoration-offset: 0%;
  text-decoration-thickness: 0%;
  text-decoration-skip-ink: auto;
  color: #333; /* SAME COLOR AS NORMAL TEXT, NOT BLUE */
}

.page-indicator {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #999;
  font-weight: 400;
  font-style: normal;
}

/* Additional topics styling */
.additional-topics {
  border-top: 1px solid #e9ecef;
  margin-top: 24px;
  padding-top: 24px;
}

.additional-topics .topic-header {
  background-color: transparent;
}

.additional-topics .subtopic-item {
  background-color: transparent;
}

.additional-topics .subtopic-item:hover {
  background-color: transparent;
}

/* Scrollbar styling */
.topics-list::-webkit-scrollbar {
  width: 6px;
}

.topics-list::-webkit-scrollbar-track {
  background: transparent;
}

.topics-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.topics-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
