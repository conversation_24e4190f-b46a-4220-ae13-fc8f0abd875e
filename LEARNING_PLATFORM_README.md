# Somayya Academy Learning Platform

A comprehensive Angular 20 learning management system with advanced features for course content delivery, text highlighting, audio playback, and note-taking.

## Features

### 🎯 Core Functionality
- **Three-Panel Layout**: Left sidebar (topics), middle content area, right controls
- **Course Navigation**: Expandable topic tree with sub-topics and page indicators
- **Content Display**: Rich text content with embedded diagrams and formulas
- **Text Highlighting**: Select and highlight text with multiple colors
- **Audio Integration**: Audio playback for each content section
- **Note Taking**: Rich text editor with formatting options
- **Bookmarks**: Save and manage bookmarks for quick navigation
- **User Preferences**: Adjustable text size and highlight colors

### 🎨 UI Components

#### Left Sidebar - Course Topics
- **Bookmarks & Annotations** header button
- **Course Structure** with expandable topics
- **Sub-topics** with current page indicators
- **Visual feedback** for active selections

#### Middle Section - Content Viewer
- **Audio Player** with play/pause, progress bar, and time display
- **Content Area** with rich text, diagrams, and highlighting support
- **Text Selection** popup for highlighting
- **Responsive text sizing** based on user preferences

#### Right Sidebar - Controls
- **Page Navigation** with numbered buttons and bookmark toggle
- **Text Size Controls** (Small, Normal, Large)
- **Audio List** with playback controls
- **Color Picker** for text highlighting (6 colors)
- **Rich Text Notepad** with formatting toolbar

### 🔧 Technical Implementation

#### Architecture
- **Angular 20** with SSR (Server-Side Rendering)
- **Zoneless** change detection for better performance
- **Standalone Components** for modularity
- **Signal-based** reactive state management
- **Injectable Services** for data and audio management

#### Key Services
- **CourseService**: Manages course data, highlights, bookmarks, and notes
- **AudioService**: Handles audio playback, playlist management, and controls

#### Data Models
```typescript
interface Course {
  id: string;
  title: string;
  topics: Topic[];
}

interface Topic {
  id: string;
  title: string;
  subTopics?: SubTopic[];
  pages?: CoursePage[];
  currentPage: number;
  expanded?: boolean;
}

interface CoursePage {
  id: string;
  pageNumber: number;
  title: string;
  contents: CourseContent[];
  audioUrl?: string;
}
```

### 🎵 Audio Management
- **Multiple Audio Tracks**: Support for different audio files per content section
- **Playlist Management**: Navigate between audio tracks
- **Progress Tracking**: Visual progress bar with click-to-seek functionality
- **Auto-play Next**: Automatic progression through playlist

### 📝 Content Management
- **Rich Text Content**: HTML-based content with styling support
- **Embedded Diagrams**: Visual representations of concepts
- **Highlighted Text**: Pre-highlighted important content
- **Interactive Elements**: Clickable and selectable text

### 💾 Data Persistence
- **LocalStorage**: Saves user preferences, highlights, bookmarks, and notes
- **Browser Compatibility**: Works across modern browsers
- **SSR Safe**: Proper handling of browser-only APIs

### 🎨 Styling & Design
- **Bootstrap Integration**: Responsive grid and components
- **Custom CSS**: Tailored styling to match design requirements
- **Color Scheme**: Professional blue and gray palette
- **Responsive Design**: Mobile and desktop friendly

## Installation & Setup

### Prerequisites
- Node.js 18+ 
- Angular CLI 20+

### Installation
```bash
# Install dependencies
npm install

# Install additional packages
npm install bootstrap @angular/cdk ngx-highlightjs highlight.js quill ngx-quill --legacy-peer-deps
```

### Development
```bash
# Start development server
ng serve

# Build for production
ng build

# Run tests
ng test
```

### Project Structure
```
src/
├── app/
│   ├── components/
│   │   ├── learning-page/          # Main container component
│   │   ├── topic-sidebar/          # Left sidebar with topics
│   │   ├── content-viewer/         # Middle content area
│   │   └── control-sidebar/        # Right controls panel
│   ├── services/
│   │   ├── course.service.ts       # Course data management
│   │   └── audio.service.ts        # Audio playback management
│   ├── models/
│   │   └── course.model.ts         # TypeScript interfaces
│   └── styles.css                  # Global styles
└── public/
    └── assets/
        └── audio/                  # Audio files directory
```

## Usage

### Navigation
1. **Select Topics**: Click on topics in the left sidebar to expand sub-topics
2. **Page Navigation**: Use numbered buttons in the right sidebar to navigate pages
3. **Bookmarks**: Click the bookmark button to save current page

### Text Highlighting
1. **Select Text**: Click and drag to select text in the content area
2. **Choose Color**: Select highlight color from the color picker
3. **Apply Highlight**: Click the highlight button in the popup

### Audio Playback
1. **Play/Pause**: Use the audio controls in the content area
2. **Seek**: Click on the progress bar to jump to specific time
3. **Playlist**: Select different audio tracks from the audio list

### Note Taking
1. **Rich Text**: Use the formatting toolbar for text styling
2. **Auto-save**: Notes are automatically saved when you click outside the editor
3. **Page-specific**: Notes are saved per page and topic

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance
- **Bundle Size**: ~550KB (including all features)
- **Load Time**: < 2 seconds on modern connections
- **Memory Usage**: Optimized for long study sessions

## Future Enhancements
- Video content support
- Collaborative features
- Progress tracking
- Quiz integration
- Export functionality
- Mobile app version

## License
This project is part of the Somayya Academy educational platform.
