# Backend API Implementation Guide

## Overview
This guide explains how to implement a backend API that supports dynamic content management, persistent highlighting, bookmarks, and notes for the course content management system.

## API Endpoints

### 1. Content Management

#### Get Page Content
```
GET /api/pages/{pageId}
```
**Response:**
```json
{
  "id": "page_1_1",
  "title": "Kinematics of Particle",
  "topicId": "rectilinear_motion",
  "pageNumber": 1,
  "totalPages": 4,
  "audioFile": "/assets/audio/kinematics.mp3",
  "items": [
    {
      "id": "item_1",
      "type": "text",
      "content": "<h3>Kinematics of Particle</h3><p>It is the study of bodies in motion...</p>",
      "order": 1,
      "metadata": {
        "className": "content-item",
        "highlights": []
      }
    },
    {
      "id": "item_2",
      "type": "diagram",
      "content": "",
      "order": 2,
      "metadata": {
        "config": {
          "type": "translation",
          "shapes": [],
          "arrows": [],
          "labels": []
        }
      }
    }
  ]
}
```

#### Update Page Content
```
PUT /api/pages/{pageId}
```
**Request Body:** Same as GET response

### 2. Highlight Management

#### Save Highlight
```
POST /api/highlights
```
**Request Body:**
```json
{
  "pageId": "page_1_1",
  "userId": "user123",
  "text": "selected text",
  "color": "yellow",
  "startOffset": 150,
  "endOffset": 200,
  "elementId": "item_1"
}
```

#### Get Highlights
```
GET /api/highlights/{pageId}?userId={userId}
```

#### Delete Highlight
```
DELETE /api/highlights/{highlightId}
```

### 3. Bookmark Management

#### Save Bookmark
```
POST /api/bookmarks
```
**Request Body:**
```json
{
  "pageId": "page_1_1",
  "userId": "user123",
  "title": "Important Concept",
  "description": "Key point about kinematics"
}
```

#### Get Bookmarks
```
GET /api/bookmarks?userId={userId}
GET /api/bookmarks/{pageId}?userId={userId}
```

### 4. Notes Management

#### Save Note
```
POST /api/notes
```
**Request Body:**
```json
{
  "pageId": "page_1_1",
  "userId": "user123",
  "content": "<p>My notes about this topic...</p>"
}
```

## Database Schema

### Pages Table
```sql
CREATE TABLE pages (
  id VARCHAR(50) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  topic_id VARCHAR(50) NOT NULL,
  page_number INT NOT NULL,
  total_pages INT NOT NULL,
  audio_file VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Content Items Table
```sql
CREATE TABLE content_items (
  id VARCHAR(50) PRIMARY KEY,
  page_id VARCHAR(50) NOT NULL,
  type ENUM('text', 'diagram', 'audio', 'video', 'image') NOT NULL,
  content TEXT,
  order_index INT NOT NULL,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE
);
```

### Highlights Table
```sql
CREATE TABLE highlights (
  id VARCHAR(50) PRIMARY KEY,
  page_id VARCHAR(50) NOT NULL,
  user_id VARCHAR(50) NOT NULL,
  text TEXT NOT NULL,
  color VARCHAR(20) NOT NULL,
  start_offset INT NOT NULL,
  end_offset INT NOT NULL,
  element_id VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE
);
```

### Bookmarks Table
```sql
CREATE TABLE bookmarks (
  id VARCHAR(50) PRIMARY KEY,
  page_id VARCHAR(50) NOT NULL,
  user_id VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE
);
```

### Notes Table
```sql
CREATE TABLE notes (
  id VARCHAR(50) PRIMARY KEY,
  page_id VARCHAR(50) NOT NULL,
  user_id VARCHAR(50) NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE
);
```

## Implementation Examples

### Node.js/Express Example
```javascript
// pages.js
app.get('/api/pages/:pageId', async (req, res) => {
  try {
    const page = await Page.findById(req.params.pageId);
    const items = await ContentItem.find({ page_id: req.params.pageId })
                                   .sort({ order_index: 1 });
    
    res.json({
      ...page,
      items: items
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// highlights.js
app.post('/api/highlights', async (req, res) => {
  try {
    const highlight = new Highlight({
      id: generateId(),
      ...req.body,
      timestamp: new Date()
    });
    
    await highlight.save();
    res.json(highlight);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## Frontend Integration

The Angular frontend automatically handles:
1. **Fallback to localStorage** when API is unavailable
2. **Real-time highlighting** with persistent storage
3. **Dynamic content rendering** based on content type
4. **Responsive diagram generation** from backend configuration

## Content Types Supported

1. **Text**: HTML content with highlighting support
2. **Diagrams**: SVG-based diagrams with configurable shapes, arrows, and labels
3. **Audio**: Audio files with playback controls
4. **Video**: Video files with controls
5. **Images**: Images with optional captions

## Security Considerations

1. **Authentication**: Implement JWT or session-based auth
2. **Authorization**: Ensure users can only access their own data
3. **Input Validation**: Sanitize all user inputs
4. **Rate Limiting**: Prevent API abuse
5. **CORS**: Configure proper CORS settings

## Performance Optimization

1. **Caching**: Cache frequently accessed content
2. **Pagination**: Implement pagination for large datasets
3. **Compression**: Use gzip compression
4. **CDN**: Serve static assets via CDN
5. **Database Indexing**: Index frequently queried fields
