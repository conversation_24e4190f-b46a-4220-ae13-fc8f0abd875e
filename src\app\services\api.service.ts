import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface ContentItem {
  id: string;
  type: 'text' | 'diagram' | 'audio' | 'video' | 'image';
  content: string;
  metadata?: any;
  order: number;
}

export interface PageContent {
  id: string;
  title: string;
  topicId: string;
  pageNumber: number;
  totalPages: number;
  items: ContentItem[];
  audioFile?: string;
  highlights?: HighlightData[];
  bookmarks?: BookmarkData[];
}

export interface HighlightData {
  id: string;
  pageId: string;
  userId: string;
  text: string;
  color: string;
  startOffset: number;
  endOffset: number;
  elementId: string;
  timestamp: Date;
}

export interface BookmarkData {
  id: string;
  pageId: string;
  userId: string;
  title: string;
  description?: string;
  timestamp: Date;
}

export interface NoteData {
  id: string;
  pageId: string;
  userId: string;
  content: string;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = 'http://localhost:3000/api'; // Your backend URL
  private userId = 'user123'; // In real app, get from auth service

  constructor(private http: HttpClient) {}

  // Content Management
  getPageContent(pageId: string): Observable<PageContent> {
    return this.http.get<PageContent>(`${this.baseUrl}/pages/${pageId}`)
      .pipe(
        catchError(error => {
          console.error('Error fetching page content:', error);
          return of(this.getMockPageContent(pageId));
        })
      );
  }

  updatePageContent(pageId: string, content: PageContent): Observable<PageContent> {
    return this.http.put<PageContent>(`${this.baseUrl}/pages/${pageId}`, content)
      .pipe(
        catchError(error => {
          console.error('Error updating page content:', error);
          return of(content);
        })
      );
  }

  // Highlight Management
  saveHighlight(highlight: Omit<HighlightData, 'id' | 'timestamp'>): Observable<HighlightData> {
    const newHighlight: HighlightData = {
      ...highlight,
      id: this.generateId(),
      timestamp: new Date()
    };

    return this.http.post<HighlightData>(`${this.baseUrl}/highlights`, newHighlight)
      .pipe(
        catchError(error => {
          console.error('Error saving highlight:', error);
          // Save to localStorage as fallback
          this.saveToLocalStorage('highlights', newHighlight);
          return of(newHighlight);
        })
      );
  }

  getHighlights(pageId: string): Observable<HighlightData[]> {
    return this.http.get<HighlightData[]>(`${this.baseUrl}/highlights/${pageId}?userId=${this.userId}`)
      .pipe(
        catchError(error => {
          console.error('Error fetching highlights:', error);
          return of(this.getFromLocalStorage('highlights', pageId));
        })
      );
  }

  deleteHighlight(highlightId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/highlights/${highlightId}`)
      .pipe(
        catchError(error => {
          console.error('Error deleting highlight:', error);
          this.removeFromLocalStorage('highlights', highlightId);
          return of();
        })
      );
  }

  // Bookmark Management
  saveBookmark(bookmark: Omit<BookmarkData, 'id' | 'timestamp'>): Observable<BookmarkData> {
    const newBookmark: BookmarkData = {
      ...bookmark,
      id: this.generateId(),
      timestamp: new Date()
    };

    return this.http.post<BookmarkData>(`${this.baseUrl}/bookmarks`, newBookmark)
      .pipe(
        catchError(error => {
          console.error('Error saving bookmark:', error);
          this.saveToLocalStorage('bookmarks', newBookmark);
          return of(newBookmark);
        })
      );
  }

  getBookmarks(pageId?: string): Observable<BookmarkData[]> {
    const url = pageId 
      ? `${this.baseUrl}/bookmarks/${pageId}?userId=${this.userId}`
      : `${this.baseUrl}/bookmarks?userId=${this.userId}`;
    
    return this.http.get<BookmarkData[]>(url)
      .pipe(
        catchError(error => {
          console.error('Error fetching bookmarks:', error);
          return of(this.getFromLocalStorage('bookmarks', pageId));
        })
      );
  }

  deleteBookmark(bookmarkId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/bookmarks/${bookmarkId}`)
      .pipe(
        catchError(error => {
          console.error('Error deleting bookmark:', error);
          this.removeFromLocalStorage('bookmarks', bookmarkId);
          return of();
        })
      );
  }

  // Notes Management
  saveNote(note: Omit<NoteData, 'id' | 'timestamp'>): Observable<NoteData> {
    const newNote: NoteData = {
      ...note,
      id: this.generateId(),
      timestamp: new Date()
    };

    return this.http.post<NoteData>(`${this.baseUrl}/notes`, newNote)
      .pipe(
        catchError(error => {
          console.error('Error saving note:', error);
          this.saveToLocalStorage('notes', newNote);
          return of(newNote);
        })
      );
  }

  getNotes(pageId: string): Observable<NoteData[]> {
    return this.http.get<NoteData[]>(`${this.baseUrl}/notes/${pageId}?userId=${this.userId}`)
      .pipe(
        catchError(error => {
          console.error('Error fetching notes:', error);
          return of(this.getFromLocalStorage('notes', pageId));
        })
      );
  }

  updateNote(noteId: string, content: string): Observable<NoteData> {
    return this.http.put<NoteData>(`${this.baseUrl}/notes/${noteId}`, { content })
      .pipe(
        catchError(error => {
          console.error('Error updating note:', error);
          return of({} as NoteData);
        })
      );
  }

  deleteNote(noteId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/notes/${noteId}`)
      .pipe(
        catchError(error => {
          console.error('Error deleting note:', error);
          this.removeFromLocalStorage('notes', noteId);
          return of();
        })
      );
  }

  // Utility Methods
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private saveToLocalStorage(type: string, data: any): void {
    const key = `${type}_${this.userId}`;
    const existing = JSON.parse(localStorage.getItem(key) || '[]');
    existing.push(data);
    localStorage.setItem(key, JSON.stringify(existing));
  }

  private getFromLocalStorage(type: string, pageId?: string): any[] {
    const key = `${type}_${this.userId}`;
    const data = JSON.parse(localStorage.getItem(key) || '[]');
    return pageId ? data.filter((item: any) => item.pageId === pageId) : data;
  }

  private removeFromLocalStorage(type: string, id: string): void {
    const key = `${type}_${this.userId}`;
    const existing = JSON.parse(localStorage.getItem(key) || '[]');
    const filtered = existing.filter((item: any) => item.id !== id);
    localStorage.setItem(key, JSON.stringify(filtered));
  }

  private getMockPageContent(pageId: string): PageContent {
    // Return mock data when API is not available
    return {
      id: pageId,
      title: 'Mock Content',
      topicId: 'mock-topic',
      pageNumber: 1,
      totalPages: 1,
      items: [
        {
          id: '1',
          type: 'text',
          content: 'This is mock content loaded when API is not available.',
          order: 1
        }
      ]
    };
  }
}
