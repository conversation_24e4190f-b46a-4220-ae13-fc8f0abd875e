.learning-container {
  height: 100vh;
  overflow: hidden;
  background-color: #ffffff;
}

.learning-layout {
  display: flex;
  height: 100%;
  gap: 0;
}

.left-sidebar {
  width: 350px;
  min-width: 250px;
  border: 1px solid #C3BFB6;
  overflow-y: auto;
  padding: 36px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

.middle-content {
  flex: 1;
  background-color: #ffffff;
  overflow-y: auto;
  padding: 0;
}

.right-sidebar {
  width: 350px;
  min-width: 280px;
  background-color: #f8f9fa;
  border-left: 1px solid #dee2e6;
  overflow-y: auto;
  box-shadow: -1px 0 3px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 1200px) {
  .left-sidebar {
    width: 250px;
    min-width: 250px;
  }
  
  .right-sidebar {
    width: 280px;
    min-width: 280px;
  }
}

@media (max-width: 992px) {
  .learning-layout {
    flex-direction: column;
  }
  
  .left-sidebar,
  .right-sidebar {
    width: 100%;
    min-width: auto;
    height: auto;
    max-height: 200px;
  }
  
  .middle-content {
    flex: 1;
    min-height: 400px;
  }
}
