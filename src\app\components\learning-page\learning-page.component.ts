import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CourseService } from '../../services/course.service';
import { Course, Topic, SubTopic, CoursePage } from '../../models/course.model';
import { TopicSidebarComponent } from '../topic-sidebar/topic-sidebar.component';
import { ContentViewerComponent } from '../content-viewer/content-viewer.component';
import { ControlSidebarComponent } from '../control-sidebar/control-sidebar.component';

@Component({
  selector: 'app-learning-page',
  standalone: true,
  imports: [
    CommonModule,
    TopicSidebarComponent,
    ContentViewerComponent,
    ControlSidebarComponent
  ],
  template: `
    <div class="learning-container">
      <div class="learning-layout">
        <!-- Left Sidebar - Topics -->
        <div class="left-sidebar">
          <app-topic-sidebar></app-topic-sidebar>
        </div>
        
        <!-- Middle Section - Content -->
        <div class="middle-content">
          <app-content-viewer></app-content-viewer>
        </div>
        
        <!-- Right Sidebar - Controls -->
        <div class="right-sidebar">
          <app-control-sidebar></app-control-sidebar>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./learning-page.component.css']
})
export class LearningPageComponent implements OnInit {
  private courseService = inject(CourseService);

  course = this.courseService.course;
  selectedTopic = this.courseService.selectedTopic;
  currentPage = this.courseService.currentPage;

  ngOnInit() {
    // Component initialization is handled by the service
  }
}
