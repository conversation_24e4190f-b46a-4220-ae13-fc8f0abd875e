export interface CourseContent {
  id: string;
  type: 'text' | 'image' | 'diagram' | 'formula';
  content: string;
  audioUrl?: string;
  order: number;
}

export interface CoursePage {
  id: string;
  pageNumber: number;
  title: string;
  contents: CourseContent[];
  audioUrl?: string;
}

export interface SubTopic {
  id: string;
  title: string;
  pages: CoursePage[];
  currentPage: number;
}

export interface Topic {
  id: string;
  title: string;
  subTopics?: SubTopic[];
  pages?: CoursePage[];
  currentPage: number;
  expanded?: boolean;
}

export interface Course {
  id: string;
  title: string;
  topics: Topic[];
}

export interface Highlight {
  id: string;
  topicId: string;
  pageId: string;
  contentId: string;
  text: string;
  color: string;
  startOffset: number;
  endOffset: number;
  timestamp: Date;
}

export interface Bookmark {
  id: string;
  topicId: string;
  pageId: string;
  title: string;
  timestamp: Date;
}

export interface Note {
  id: string;
  topicId: string;
  pageId: string;
  content: string;
  timestamp: Date;
}

export interface AudioItem {
  id: string;
  title: string;
  url: string;
  duration: number;
  contentId?: string;
}

export interface UserPreferences {
  textSize: 'small' | 'normal' | 'large';
  highlightColor: string;
}
