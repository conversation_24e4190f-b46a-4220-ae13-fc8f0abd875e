.content-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

/* Audio Section */
.audio-section {
  padding: 12px 20px;
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.audio-player {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.audio-info {
  min-width: 120px;
}

.audio-title {
  font-weight: 500;
  color: #495057;
  font-size: 13px;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.btn-audio {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: #6c757d;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.btn-audio:hover {
  background-color: #5a6268;
}

.audio-progress {
  flex: 1;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  flex: 1;
  min-width: 150px;
}

.progress-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.1s ease;
  border-radius: 3px;
}

.time-display {
  display: flex;
  gap: 4px;
  font-size: 11px;
  color: #6c757d;
  min-width: 60px;
}

.current-time::after {
  content: ":";
  margin: 0 2px;
}

/* Content Section */
.content-section {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.content-body {
  max-width: none;
  margin: 0;
  line-height: 1.6;
  color: #333333;
  user-select: text;
  font-size: 14px;
}

.content-body.text-small {
  font-size: 14px;
}

.content-body.text-normal {
  font-size: 16px;
}

.content-body.text-large {
  font-size: 18px;
}

.content-item {
  margin-bottom: 20px;
}

.content-item h3 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 1.25em;
}

.content-item p {
  margin-bottom: 12px;
  text-align: justify;
}

.content-item ul {
  margin-left: 20px;
  margin-bottom: 16px;
}

.content-item li {
  margin-bottom: 8px;
}

.content-item strong {
  font-weight: 600;
  color: #2c3e50;
}

.no-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-style: italic;
}

/* Selection Popup */
.selection-popup {
  position: fixed;
  z-index: 1000;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 4px;
}

.highlight-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: background-color 0.2s ease;
}

.highlight-btn:hover {
  background-color: #f0f0f0;
}

/* Highlight Styles */
.highlight-yellow {
  background-color: #ffff99 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-green {
  background-color: #90ee90 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-blue {
  background-color: #87ceeb !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-pink {
  background-color: #ffb6c1 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-orange {
  background-color: #ffd700 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-purple {
  background-color: #dda0dd !important;
  padding: 1px 2px;
  border-radius: 2px;
}

/* Scrollbar */
.content-section::-webkit-scrollbar {
  width: 8px;
}

.content-section::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.content-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.content-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Diagram Styles */
.diagram-container {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin: 40px 0;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.diagram-section {
  text-align: center;
}

.diagram-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.diagram-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.shape-box {
  position: relative;
  width: 80px;
  height: 60px;
  border: 3px solid #333;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.shape-box.rotated {
  transform: rotate(15deg);
}

.corner-label {
  position: absolute;
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.corner-label.top-left {
  top: 4px;
  left: 6px;
}

.corner-label.top-right {
  top: 4px;
  right: 6px;
}

.corner-label.bottom-left {
  bottom: 4px;
  left: 6px;
}

.corner-label.bottom-right {
  bottom: 4px;
  right: 6px;
}

.arrow-right {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
}

.arrow-curved {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
}

/* Responsive */
@media (max-width: 768px) {
  .content-section {
    padding: 16px;
  }

  .audio-section {
    padding: 12px 16px;
  }

  .audio-progress {
    min-width: 150px;
  }

  .diagram-container {
    flex-direction: column;
    gap: 30px;
    padding: 20px;
  }

  .shape-box {
    width: 60px;
    height: 45px;
  }
}
