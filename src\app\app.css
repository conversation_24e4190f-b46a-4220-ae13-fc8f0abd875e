:host {
  display: block;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #343a40;
  color: white;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-bottom: 1px solid #495057;
}

.header h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 280px;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
  flex-shrink: 0;
  box-shadow: 2px 0 4px rgba(0,0,0,0.05);
}

.content-area {
  flex: 1;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0 1px;
  box-shadow: 0 0 8px rgba(0,0,0,0.05);
}

.control-panel {
  width: 300px;
  border-left: 1px solid #dee2e6;
  overflow-y: auto;
  flex-shrink: 0;
  box-shadow: -2px 0 4px rgba(0,0,0,0.05);
}