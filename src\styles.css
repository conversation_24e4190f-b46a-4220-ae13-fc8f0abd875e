/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
@import 'bootstrap/dist/css/bootstrap.min.css';
@import 'highlight.js/styles/default.css';
@import 'quill/dist/quill.snow.css';

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #FFFBF3;
  color: #333333;
  font-size: 14px;
  line-height: 1.5;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Text highlighting styles */
.highlight-yellow {
  background-color: #ffff99 !important;
}

.highlight-green {
  background-color: #90ee90 !important;
}

.highlight-blue {
  background-color: #87ceeb !important;
}

.highlight-pink {
  background-color: #ffb6c1 !important;
}

.highlight-orange {
  background-color: #ffd700 !important;
}

.highlight-purple {
  background-color: #dda0dd !important;
}

/* Text size classes */
.text-small {
  font-size: 0.875rem;
}

.text-normal {
  font-size: 1rem;
}

.text-large {
  font-size: 1.125rem;
}
